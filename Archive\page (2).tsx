'use client'

import { useAuth } from '../../contexts/AuthContext'

export default function DashboardPage() {
  const { user, isLoading } = useAuth()
  
  return (
    <div className="container">
      <div className="dashboard-header flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-secondary">Manage your subscriptions, maintenance, support and warranties</p>
        </div>
        <div className="flex items-center">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search renewals..."
              className="input search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
          <button className="btn btn-primary">
            <span style={{ marginRight: '8px' }}>+</span>
            Add Renewal
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="card stat-card">
          <div className="icon-container icon-blue">
            <span>📄</span>
          </div>
          <div>
            <p className="text-secondary text-sm">Total Renewals</p>
            <p className="text-2xl font-bold">13</p>
          </div>
        </div>
        
        <div className="card stat-card">
          <div className="icon-container icon-orange">
            <span>⏰</span>
          </div>
          <div>
            <p className="text-secondary text-sm">Renewals Due</p>
            <p className="text-2xl font-bold">0</p>
          </div>
        </div>
        
        <div className="card stat-card">
          <div className="icon-container icon-green">
            <span>🏢</span>
          </div>
          <div>
            <p className="text-secondary text-sm">Vendors</p>
            <p className="text-2xl font-bold">8</p>
          </div>
        </div>
        
        <div className="card stat-card">
          <div className="icon-container icon-purple">
            <span>💰</span>
          </div>
          <div>
            <p className="text-secondary text-sm">Annual Spend</p>
            <p className="text-2xl font-bold">$817,240</p>
          </div>
        </div>
      </div>
    </div>
  )
}






