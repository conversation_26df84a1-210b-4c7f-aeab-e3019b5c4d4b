'use client'

import { useEffect, useState } from 'react'

export default function EnvDebugger() {
  const [clientEnvVars, setClientEnvVars] = useState<Record<string, string | undefined>>({})
  
  // These are server-side environment variables (available during build/SSR)
  const serverEnvVars = {
    NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,
    NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,
  }
  
  useEffect(() => {
    // These are client-side environment variables (available in browser)
    const vars: Record<string, string | undefined> = {}
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('NEXT_PUBLIC_')) {
        vars[key] = process.env[key]
      }
    })
    setClientEnvVars(vars)
  }, [])
  
  return (
    <div className="p-4 bg-gray-100 rounded-lg my-4 max-w-2xl mx-auto text-sm">
      <h3 className="font-bold mb-2">Environment Variables Debug</h3>
      
      <h4 className="font-semibold mt-2">Server-side Environment Variables:</h4>
      <pre className="bg-white p-3 rounded overflow-auto max-h-60">
        {JSON.stringify(serverEnvVars, null, 2)}
      </pre>
      
      <h4 className="font-semibold mt-4">Client-side Environment Variables:</h4>
      <pre className="bg-white p-3 rounded overflow-auto max-h-60">
        {JSON.stringify(clientEnvVars, null, 2)}
      </pre>
    </div>
  )
}
