# AWS RDS Database Setup Commands

This document provides the AWS CLI commands needed to set up your RDS database with both traditional password authentication and IAM authentication options.

## Prerequisites

1. AWS CLI installed and configured
2. Appropriate IAM permissions for RDS, SSM Parameter Store, and IAM operations
3. Your AWS region set (default: ca-central-1)

## Environment Variables

Set these environment variables or replace them in the commands below:
```bash
export AWS_REGION="ca-central-1"
export DB_INSTANCE_IDENTIFIER="renewtrack-prod-ca-central-1-rds"
export DB_USERNAME="renewtrack_admin"
export DB_IAM_USERNAME="renewtrack_iam_user"
export KMS_KEY_ID="your-kms-key-id"  # Optional, for encryption
```

## Option 1: Traditional Username/Password Authentication

### 1. Store Database Credentials in Parameter Store

```bash
# Store database username
aws ssm put-parameter \
    --name "/renewtrack/db/username" \
    --value "$DB_USERNAME" \
    --type "String" \
    --region $AWS_REGION

# Store database password (replace 'your-secure-password' with actual password)
aws ssm put-parameter \
    --name "/renewtrack/db/password" \
    --value "your-secure-password" \
    --type "SecureString" \
    --region $AWS_REGION

# Store database host
aws ssm put-parameter \
    --name "/renewtrack/db/host" \
    --value "$DB_INSTANCE_IDENTIFIER.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com" \
    --type "String" \
    --region $AWS_REGION

# Store database name
aws ssm put-parameter \
    --name "/renewtrack/db/name" \
    --value "$DB_INSTANCE_IDENTIFIER" \
    --type "String" \
    --region $AWS_REGION
```

### 2. Set Environment Variable
```bash
# For traditional password auth
export USE_IAM_DB_AUTH=false
```

## Option 2: IAM Database Authentication (More Secure)

### 1. Enable IAM Database Authentication on RDS Instance

```bash
aws rds modify-db-instance \
    --db-instance-identifier $DB_INSTANCE_IDENTIFIER \
    --enable-iam-database-authentication \
    --apply-immediately \
    --region $AWS_REGION
```

### 2. Create IAM Database User in PostgreSQL

Connect to your database and run:
```sql
-- Connect as master user first
CREATE USER renewtrack_iam_user;
GRANT rds_iam TO renewtrack_iam_user;
GRANT CONNECT ON DATABASE your_database_name TO renewtrack_iam_user;
GRANT USAGE ON SCHEMA public TO renewtrack_iam_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO renewtrack_iam_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO renewtrack_iam_user;
```

### 3. Create IAM Policy for RDS Connect

```bash
# Create IAM policy for RDS connect
aws iam create-policy \
    --policy-name RenewTrackRDSConnectPolicy \
    --policy-document '{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": [
                    "rds-db:connect"
                ],
                "Resource": [
                    "arn:aws:rds-db:'$AWS_REGION':*:dbuser:'$DB_INSTANCE_IDENTIFIER'/'$DB_IAM_USERNAME'"
                ]
            }
        ]
    }' \
    --region $AWS_REGION
```

### 4. Store IAM Database Credentials

```bash
# Store IAM database username
aws ssm put-parameter \
    --name "/renewtrack/db/username" \
    --value "$DB_IAM_USERNAME" \
    --type "String" \
    --region $AWS_REGION

# Store database host
aws ssm put-parameter \
    --name "/renewtrack/db/host" \
    --value "$DB_INSTANCE_IDENTIFIER.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com" \
    --type "String" \
    --region $AWS_REGION

# Store database name
aws ssm put-parameter \
    --name "/renewtrack/db/name" \
    --value "$DB_INSTANCE_IDENTIFIER" \
    --type "String" \
    --region $AWS_REGION
```

### 5. Set Environment Variable
```bash
# For IAM authentication
export USE_IAM_DB_AUTH=true
```

## Update CloudFormation Template

Update your `cloudformation/parameter-store-role.yaml` to include RDS permissions:

```yaml
Resources:
  ParameterStoreAccessRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: RenewTrackParameterStoreAccess
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - !Ref ParameterStoreAccessPolicy
        - !Ref RDSConnectPolicy  # Add this line

  RDSConnectPolicy:  # Add this new policy
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: RenewTrackRDSConnectPolicy
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - rds-db:connect
            Resource:
              - !Sub arn:aws:rds-db:${AWS::Region}:${AWS::AccountId}:dbuser:${DBInstanceIdentifier}/${DBIAMUsername}

  ParameterStoreAccessPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: RenewTrackParameterStorePolicy
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ssm:GetParameter
              - ssm:GetParameters
            Resource:
              - !Sub arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/renewtrack/db/*
          - Effect: Allow
            Action:
              - kms:Decrypt
            Resource:
              - !Sub arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/${KMSKeyId}
            Condition:
              StringEquals:
                kms:ViaService: !Sub ssm.${AWS::Region}.amazonaws.com

Parameters:
  DBInstanceIdentifier:
    Type: String
    Default: renewtrack-prod-ca-central-1-rds
  DBIAMUsername:
    Type: String
    Default: renewtrack_iam_user
  KMSKeyId:
    Type: String
    Description: KMS Key ID for Parameter Store encryption
```

## Deploy CloudFormation Stack

```bash
aws cloudformation deploy \
    --template-file cloudformation/parameter-store-role.yaml \
    --stack-name renewtrack-parameter-store-role \
    --capabilities CAPABILITY_NAMED_IAM \
    --parameter-overrides \
        DBInstanceIdentifier=$DB_INSTANCE_IDENTIFIER \
        DBIAMUsername=$DB_IAM_USERNAME \
        KMSKeyId=$KMS_KEY_ID \
    --region $AWS_REGION
```

## Verification Commands

### Test Parameter Store Access
```bash
# Test reading parameters
aws ssm get-parameter --name "/renewtrack/db/username" --region $AWS_REGION
aws ssm get-parameter --name "/renewtrack/db/host" --region $AWS_REGION
aws ssm get-parameter --name "/renewtrack/db/name" --region $AWS_REGION

# For password auth only
aws ssm get-parameter --name "/renewtrack/db/password" --with-decryption --region $AWS_REGION
```

### Test IAM Database Authentication (if using Option 2)
```bash
# Generate auth token (requires AWS CLI and proper IAM permissions)
aws rds generate-db-auth-token \
    --hostname $DB_INSTANCE_IDENTIFIER.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com \
    --port 5432 \
    --username $DB_IAM_USERNAME \
    --region $AWS_REGION
```

## Environment Variables for Your Application

Add these to your production environment:

```bash
# Required for both options
NEXT_PUBLIC_AWS_REGION=ca-central-1
PARAMETER_STORE_ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT_ID:role/RenewTrackParameterStoreAccess

# Choose authentication method
USE_IAM_DB_AUTH=true  # or false for password auth

# For IAM auth (optional fallback)
DB_IAM_USER=renewtrack_iam_user
```

## Security Recommendations

1. **Use IAM Authentication** when possible - it's more secure
2. **Rotate passwords regularly** if using traditional auth
3. **Use least privilege** IAM policies
4. **Enable encryption** for Parameter Store values
5. **Monitor database connections** and access patterns
6. **Use VPC security groups** to restrict database access
