import { NextRequest, NextResponse } from 'next/server';
import { getTenantByDomain } from '../../../../lib/clients';
import { z } from 'zod'; // Add zod for input validation

// Define schema for request validation
const requestSchema = z.object({
  domain: z.string().min(3).max(255).regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/)
});

export async function GET(request: NextRequest) {
  // Rate limiting headers
  const response = new NextResponse();
  response.headers.set('X-RateLimit-Limit', '100');
  response.headers.set('X-RateLimit-Remaining', '99'); // This would be dynamic in a real implementation
  
  const searchParams = request.nextUrl.searchParams;
  const domain = searchParams.get('domain');
  
  // Validate input
  try {
    requestSchema.parse({ domain });
  } catch (error) {
    return NextResponse.json({ 
      error: 'Invalid domain format',
      details: error instanceof Error ? error.message : 'Validation error'
    }, { status: 400 });
  }
  
  if (!domain) {
    return NextResponse.json({ error: 'Domain parameter is required' }, { status: 400 });
  }
  
  try {
    const tenant = await getTenantByDomain(domain);
    
    if (!tenant) {
      return NextResponse.json({ error: 'Tenant not found for this domain' }, { status: 404 });
    }
    
    // Don't expose internal IDs or sensitive data
    return NextResponse.json({ 
      id: tenant.tenant_id,
      name: tenant.tenant_name,
      subdomain: tenant.subdomain,
      status: tenant.status,
      // Don't return schema_name as it's internal implementation detail
    });
  } catch (error) {
    console.error('Error in tenant domain lookup:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
