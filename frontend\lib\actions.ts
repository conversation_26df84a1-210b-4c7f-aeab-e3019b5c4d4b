'use server'

import { verifySession, hasRole } from './dal'
import { cookies } from 'next/headers'

// ISSUE: CSRF token implementation could be strengthened
// RECOMMENDATION: Use double submit cookie pattern with secure, httpOnly flags

const verifyCSRFToken = async (formData: FormData) => {
  const cookieStore = cookies();
  const storedToken = cookieStore.get('csrf-token')?.value;
  const formToken = formData.get('csrf-token') as string;
  
  if (!storedToken || !formToken || storedToken !== formToken) {
    // RECOMMENDATION: Add logging for security events
    console.error('CSRF token validation failed', {
      hasStoredToken: !!storedToken,
      hasFormToken: !!formToken,
      // Don't log the actual tokens for security reasons
    });
    throw new Error('CSRF token validation failed');
  }
  
  return true;
}

// RECOMMENDATION: Add a function to generate CSRF tokens
export function generateCSRFToken() {
  const token = crypto.randomBytes(32).toString('hex');
  
  // Set the cookie with proper security flags
  cookies().set('csrf-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 3600 // 1 hour
  });
  
  return token;
}

// Example of a secure server action
export async function updateUserProfile(formData: FormData) {
  // 1. Verify CSRF token
  await verifyCSRFToken(formData)
  
  // 2. Verify user session
  const session = await verifySession()
  if (!session) {
    throw new Error('Unauthorized')
  }
  
  // 3. Check permissions for this action
  if (!hasRole(session, 'user')) {
    throw new Error('Insufficient permissions')
  }
  
  // 4. Sanitize and validate input data
  const name = String(formData.get('name'))
  if (!name || name.length < 2) {
    throw new Error('Invalid name')
  }
  
  // 5. Perform the action
  try {
    // Update user profile logic here
    return { success: true }
  } catch (error) {
    console.error('Profile update error:', error)
    throw new Error('Failed to update profile')
  }
}

