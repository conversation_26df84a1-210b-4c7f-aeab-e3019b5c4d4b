import { NextRequest, NextResponse } from 'next/server';
import { getClientByDomain, getClientByEmailDomain } from '@/lib/clients';
import { verifySession } from '@/lib/dal';

export async function GET(request: NextRequest) {
  // Verify authentication
  const session = await verifySession();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Get domain or email from query params
  const { searchParams } = new URL(request.url);
  const domain = searchParams.get('domain');
  const email = searchParams.get('email');
  
  if (!domain && !email) {
    return NextResponse.json({ error: 'Domain or email parameter is required' }, { status: 400 });
  }
  
  try {
    let client;
    
    if (email) {
      client = await getClientByEmailDomain(email);
    } else if (domain) {
      client = await getClientByDomain(domain);
    }
    
    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }
    
    // Return sanitized client data
    return NextResponse.json({
      id: client.id,
      name: client.name,
      domain: client.domain,
      status: client.status,
      settings: client.settings || {
        theme: 'default',
        features: {}
      },
      createdAt: client.created_at,
      updatedAt: client.updated_at
    });
  } catch (error) {
    console.error('Error in client lookup:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}





