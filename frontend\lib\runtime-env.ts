// This utility helps load environment variables at runtime
// Useful for debugging environment variable issues

interface RuntimeEnv {
  NEXT_PUBLIC_AWS_REGION?: string;
  NEXT_PUBLIC_AWS_USER_POOLS_ID?: string;
  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID?: string;
  NEXT_PUBLIC_AWS_COGNITO_DOMAIN?: string;
  NEXT_PUBLIC_REDIRECT_SIGN_IN?: string;
  NEXT_PUBLIC_REDIRECT_SIGN_OUT?: string;
}

// Try to load environment variables from different sources
export function getRuntimeEnv(): RuntimeEnv {
  // First try window.__NEXT_DATA__.props.pageProps.env if it exists (SSR injected)
  if (typeof window !== 'undefined' && 
      window.__NEXT_DATA__?.props?.pageProps?.env) {
    return window.__NEXT_DATA__.props.pageProps.env;
  }
  
  // Then try process.env (should work in both server and client contexts)
  const env: RuntimeEnv = {
    NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,
    NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,
    NEXT_PUBLIC_REDIRECT_SIGN_IN: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
    NEXT_PUBLIC_REDIRECT_SIGN_OUT: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT,
  };
  
  return env;
}