'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Sidebar from '@/components/layout/Sidebar'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)
  
  useEffect(() => {
    console.log('🏠 [DASHBOARD-LAYOUT] Auth state changed:', {
      isLoading,
      isAuthenticated,
      isRedirecting,
      timestamp: new Date().toLocaleTimeString()
    });

    // Check if this is an OAuth callback (has code parameter)
    const urlParams = new URLSearchParams(window.location.search)
    const hasOAuthCode = urlParams.has('code')

    if (hasOAuthCode) {
      console.log('🔄 [DASHBOARD-LAYOUT] OAuth callback detected, waiting for Amplify to process...')
      // Give Amplify extra time to process OAuth callback
      return
    }

    // Only redirect if authentication check is complete and user is not authenticated
    if (!isLoading && !isAuthenticated && !isRedirecting) {
      setIsRedirecting(true)
      console.log('❌ [DASHBOARD-LAYOUT] User not authenticated, redirecting to login')
      router.push('/login')
    } else if (!isLoading && isAuthenticated) {
      console.log('✅ [DASHBOARD-LAYOUT] User is authenticated, showing dashboard')

      // Clean up URL if it has OAuth parameters
      if (hasOAuthCode) {
        const cleanUrl = window.location.pathname
        window.history.replaceState({}, document.title, cleanUrl)
      }
    }
  }, [isLoading, isAuthenticated, router, isRedirecting])

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin"></div>
        <p className="ml-4 text-lg">Loading your dashboard...</p>
      </div>
    )
  }

  // Don't render anything while redirecting
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin"></div>
        <p className="ml-4 text-lg">Redirecting to login...</p>
      </div>
    )
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <main className="main-content">
        {children}
      </main>
    </div>
  )
}







