'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { fetchAuthSession, signInWithRedirect } from 'aws-amplify/auth'
import { setAuthCookie } from '@/lib/auth'
import { configureAmplify } from '@/lib/amplify-config'

export default function CallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [error, setError] = useState<string | null>(null)
  const [status, setStatus] = useState('Completing sign-in...')

  useEffect(() => {
    let isMounted = true

    async function handleCallback() {
      try {
        console.log('Callback page loaded, handling OAuth callback...')

        // Ensure Amplify is configured
        configureAmplify()

        // Get the code from URL
        const code = searchParams.get('code')
        if (!code) {
          throw new Error('No authorization code found in URL')
        }

        console.log('Authorization code found:', code.substring(0, 10) + '...')

        try {
          // Try to fetch the session first to see if we're already authenticated
          const existingSession = await fetchAuthSession();
          if (existingSession?.tokens?.idToken) {
            console.log('User is already signed in, using existing session');
            setAuthCookie(existingSession.tokens.idToken.toString());
            localStorage.setItem('isAuthenticated', 'true');
            router.push('/dashboard');
            return;
          }

          // If not already authenticated, complete the sign-in
          await signInWithRedirect();
          console.log('Redirect handled successfully');

          // Then fetch the session
          const session = await fetchAuthSession({ forceRefresh: true });
          console.log('Session fetched after handling redirect:', session ? 'success' : 'failed');

          if (session?.tokens?.idToken) {
            console.log('ID token found in session');
            // Set the cookie with the token
            setAuthCookie(session.tokens.idToken.toString());

            // Set flag in localStorage for cross-tab auth state
            localStorage.setItem('isAuthenticated', 'true');

            // Redirect to dashboard
            router.push('/dashboard');
          } else {
            throw new Error('No ID token found in session after handling redirect');
          }
        } catch (error: any) {
          console.error('Error handling redirect:', error);
          // If the error is because user is already signed in, redirect to dashboard
          if (error.message && error.message.includes('already signed in')) {
            console.log('User is already signed in, redirecting to dashboard');
            // Try to get the session directly
            const session = await fetchAuthSession({ forceRefresh: true });
            if (session?.tokens?.idToken) {
              setAuthCookie(session.tokens.idToken.toString());
              localStorage.setItem('isAuthenticated', 'true');
            }
            router.push('/dashboard');
            return;
          }
          throw error;
        }
      } catch (error: any) {
        console.error('Authentication error:', error)
        if (isMounted) {
          setError(`Authentication failed: ${error.message}`)
          setTimeout(() => {
            router.push('/login')
          }, 2000)
        }
      }
    }

    handleCallback()

    return () => {
      isMounted = false
    }
  }, [router, searchParams])

  return (
    <div className="flex flex-col items-center justify-center h-screen p-4">
      {error ? (
        <div className="p-4 bg-red-100 border border-red-400 rounded max-w-md w-full">
          <p className="text-red-700">{error}</p>
          <p className="text-sm text-gray-600 mt-2">Redirecting to login page...</p>
        </div>
      ) : (
        <div className="p-4 text-center">
          <h1 className="text-2xl font-bold mb-4">Welcome Back!</h1>
          <p className="mb-4">{status}</p>
          <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin mx-auto"></div>
        </div>
      )}
    </div>
  )
}

