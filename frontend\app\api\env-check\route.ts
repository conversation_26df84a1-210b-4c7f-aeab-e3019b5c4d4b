import { NextResponse } from 'next/server'

export async function GET() {
  // This will run on the server and can access all environment variables
  const envVars = {
    NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,
    NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,
    NODE_ENV: process.env.NODE_ENV,
    // Include non-NEXT_PUBLIC vars to verify they're loaded (but don't expose sensitive ones)
    ENV_LOADED: process.env.ENV_LOADED || 'checking',
  }
  
  return NextResponse.json({ 
    envVars,
    message: 'Environment variables as seen by the server' 
  })
}