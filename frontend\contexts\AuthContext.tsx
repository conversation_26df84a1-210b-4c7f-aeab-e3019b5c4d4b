
import { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import {
  fetchAuthSession,
  signOut as amplifySignOut,
  getCurrentUser as getAmplifyCurrentUser,
  fetchUserAttributes
} from 'aws-amplify/auth';
import { User } from '@/types/user';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  signOut: () => Promise<boolean>;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  isLoading: true,
  user: null,
  signOut: async () => false,
  hasRole: () => false,
});

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setAuthState] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<User | null>(null);

  // Get current user from Amplify
  const getCurrentUser = async (): Promise<User | null> => {
    try {
      const currentUser = await getAmplifyCurrentUser();
      const attributes = await fetchUserAttributes();
      
      return {
        id: currentUser.userId,
        email: attributes.email || '',
        name: attributes.name,
        given_name: attributes.given_name,
        family_name: attributes.family_name,
        roles: attributes['custom:roles'] ? JSON.parse(attributes['custom:roles']) : []
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  };

  useEffect(() => {
    let isMounted = true;

    const checkAuth = async () => {
      if (!isMounted) return;

      try {
        console.log('🔍 [AUTH-CONTEXT] Starting authentication check...');

        // Check if this is an OAuth callback
        const urlParams = new URLSearchParams(window.location.search)
        const hasOAuthCode = urlParams.has('code')

        if (hasOAuthCode) {
          console.log('🔄 [AUTH-CONTEXT] OAuth callback detected, giving Amplify extra time...');
          // Give Amplify more time to process OAuth callback
          await new Promise(resolve => setTimeout(resolve, 5000));
        } else {
          // Small delay to ensure Amplify has processed any OAuth callbacks
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Check if authenticated with Amplify
        const session = await fetchAuthSession({ forceRefresh: false });
        const authenticated = !!session?.tokens?.idToken;

        console.log('🔍 [AUTH-CONTEXT] Session details:', {
          hasSession: !!session,
          hasTokens: !!session?.tokens,
          hasIdToken: !!session?.tokens?.idToken,
          hasAccessToken: !!session?.tokens?.accessToken,
          authenticated
        });

        if (authenticated) {
          console.log('✅ [AUTH-CONTEXT] Session is authenticated, getting user info...');

          try {
            const userInfo = await getCurrentUser();
            console.log('✅ [AUTH-CONTEXT] User info retrieved:', userInfo);

            if (isMounted && userInfo) {
              setUser(userInfo);
              setAuthState(true);
              console.log('✅ [AUTH-CONTEXT] User state updated successfully');
            } else if (isMounted) {
              console.log('❌ [AUTH-CONTEXT] Component unmounted or no user info');
              setUser(null);
              setAuthState(false);
            }
          } catch (userError) {
            console.error('❌ [AUTH-CONTEXT] Error getting user info:', userError);
            if (isMounted) {
              setUser(null);
              setAuthState(false);
            }
          }
        } else {
          console.log('❌ [AUTH-CONTEXT] No valid session found');
          if (isMounted) {
            setUser(null);
            setAuthState(false);
          }
        }
      } catch (error) {
        console.error('❌ [AUTH-CONTEXT] Auth check error:', error);
        if (isMounted) {
          setUser(null);
          setAuthState(false);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
          console.log('🏁 [AUTH-CONTEXT] Auth check complete. Final state:', {
            isAuthenticated,
            hasUser: !!user,
            isLoading: false
          });
        }
      }
    };

    checkAuth();
    
    // Listen for storage events to sync auth state across tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'isAuthenticated') {
        console.log('AuthContext: Storage event detected, rechecking auth...');
        checkAuth();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      isMounted = false;
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Role checking utility
  const hasRole = (role: string) => {
    return user?.roles?.includes(role) || false;
  };

  // Sign out function
  const signOut = async (): Promise<boolean> => {
    try {
      await amplifySignOut({ global: true });
      
      // Clear localStorage flag
      localStorage.removeItem('isAuthenticated');
      
      // Clear the auth cookie
      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
      
      // Update state
      setUser(null);
      setAuthState(false);
      
      return true;
    } catch (error) {
      console.error('Error signing out:', error);
      
      // Even if Amplify signOut fails, clear local auth state
      localStorage.removeItem('isAuthenticated');
      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
      
      // Update state
      setUser(null);
      setAuthState(false);
      
      return true; // Return true anyway to allow navigation to login page
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        signOut,
        hasRole,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook for using auth context
export const useAuth = () => useContext(AuthContext);

