/* Reset and base styles */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #6b7280;
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --sidebar-bg: #1f2937;
  --sidebar-text: #ffffff;
  --sidebar-hover: #374151;
  --border-color: #e5e7eb;
  --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.5;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  cursor: pointer;
  font-family: inherit;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
}

h1 {
  font-size: 24px;
}

h2 {
  font-size: 20px;
}

h3 {
  font-size: 18px;
}

.text-sm {
  font-size: 14px;
}

.text-xs {
  font-size: 12px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 20px;
}

.text-2xl {
  font-size: 24px;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

/* Components */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: var(--spacing-lg);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover {
  background-color: var(--background-color);
}

.input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 16px;
  width: 100%;
}

.input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.badge {
  display: inline-block;
  padding: 2px var(--spacing-sm);
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

/* Sidebar specific styles */
.sidebar {
  width: 250px;
  background-color: #1a1a1a;
  color: #ffffff;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-logo {
  padding: 20px;
  border-bottom: 1px solid #333333;
}

.sidebar-nav {
  flex: 1;
  padding: 10px 0;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  color: #e0e0e0;
  text-decoration: none;
  transition: background-color 0.2s;
  font-size: 14px;
}

.sidebar-link:hover {
  background-color: #333333;
}

.sidebar-link.active {
  background-color: #333333;
}

.sidebar-icon {
  margin-right: var(--spacing-sm);
}

.sidebar-section {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-xs);
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
}

/* User profile section in sidebar */
.sidebar-user {
  padding: 15px 20px;
  border-top: 1px solid #333333;
  display: flex;
  align-items: center;
  background-color: #1a1a1a;
  margin-top: auto;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.user-email {
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: #a0a0a0;
}

.sidebar-signout {
  background: none;
  border: none;
  color: #a0a0a0;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.sidebar-signout:hover {
  color: #ffffff;
  background-color: #333333;
}

.flex-1 {
  flex: 1;
}

/* Main content */
.main-content {
  margin-left: 250px;
  padding: var(--spacing-lg);
  min-height: 100vh;
}

/* Dashboard specific */
.dashboard-header {
  margin-bottom: var(--spacing-lg);
}

.search-container {
  position: relative;
  margin-right: var(--spacing-md);
}

.search-input {
  padding-left: 36px;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    width: 100%;
    position: relative;
    height: auto;
  }
  
  .main-content {
    margin-left: 0;
  }
}

.stat-card {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
}

.icon-blue {
  background-color: #dbeafe;
  color: var(--primary-color);
}

.icon-orange {
  background-color: #ffedd5;
  color: #f97316;
}

.icon-green {
  background-color: #dcfce7;
  color: #22c55e;
}

.icon-purple {
  background-color: #f3e8ff;
  color: #a855f7;
}

/* Loading state */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: var(--primary-color);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}



