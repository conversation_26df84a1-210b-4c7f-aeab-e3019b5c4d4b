import 'server-only'
import { cache } from 'react'
import { cookies } from 'next/headers'
import { getAmplifySSR } from './amplify-config'

export const verifySession = cache(async () => {
  const cookieStore = await cookies()
  const authCookie = cookieStore.get('idToken')
  
  if (!authCookie?.value) {
    return null
  }
  
  try {
    // For Amplify v6, we need a different approach
    // This is a simplified version - in production, you'd verify the JWT token
    const tokenParts = authCookie.value.split('.');
    if (tokenParts.length !== 3) {
      return null;
    }
    
    // Decode the payload part of the JWT
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
    
    return { 
      isAuth: true, 
      userId: payload.sub,
      email: payload.email,
      roles: payload['cognito:groups'] || []
    }
  } catch (error) {
    console.error('Auth verification error:', error)
    return null
  }
})

export const getUser = cache(async () => {
  const session = await verifySession()
  if (!session) return null
  
  // Get user data from session
  // Add your user fetching logic here
  return { id: session.userId }
})

// Role-based authorization helper
export const hasRole = (session: any, requiredRole: string) => {
  if (!session || !session.roles) return false
  return session.roles.includes(requiredRole)
}




