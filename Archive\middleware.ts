import { NextRequest, NextResponse } from 'next/server'
import { getLoginUrl } from './lib/auth'

export async function middleware(request: NextRequest) {
  // Define protected routes that require authentication
  const isProtectedRoute = request.nextUrl.pathname.startsWith('/dashboard') ||
                          request.nextUrl.pathname.startsWith('/account') ||
                          request.nextUrl.pathname.startsWith('/admin')

  // Skip middleware for callback route to avoid interfering with OAuth flow
  if (request.nextUrl.pathname.startsWith('/callback')) {
    return NextResponse.next()
  }

  // Check authentication for protected routes
  if (isProtectedRoute) {
    // Get auth cookies
    const authCookie = request.cookies.get('idToken')
    
    if (!authCookie?.value) {
      // User is not authenticated, redirect to login
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // Handle login route specifically
  if (request.nextUrl.pathname === '/login') {
    // Check if user is already authenticated
    const authCookie = request.cookies.get('idToken')
    
    if (authCookie?.value) {
      // User appears to be authenticated, redirect to dashboard
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
    
    // User is not authenticated, proceed with login
    // Don't automatically redirect to the login URL, just show the login page
    return NextResponse.next()
  }

  // For non-protected routes, continue
  return NextResponse.next()
}

// Configure middleware to run on specific paths
export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}


