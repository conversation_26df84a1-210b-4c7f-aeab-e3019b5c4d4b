import { fetchAuthSession, signOut as amplifySignOut } from 'aws-amplify/auth'
import { configureAmplify } from './amplify-config'

// Get login URL
export const getLoginUrl = () => {
  const cognitoDomain = 'auth.renewtrack.com';
  const clientId = '6fc4ks4poom3mqk5icavr7np1k';
  const redirectUri = 'http://localhost:3000/callback';
  
  return `https://${cognitoDomain}/login?client_id=${clientId}&response_type=code&scope=aws.cognito.signin.user.admin+email+openid+profile&redirect_uri=${encodeURIComponent(redirectUri)}`;
}

// Set auth cookie
export const setAuthCookie = (idToken: string) => {
  document.cookie = `idToken=${idToken}; path=/; max-age=${60*60*24*7}; SameSite=Lax`;
  console.log('Auth cookie set successfully');
}

// Check if user is authenticated
export const isAuthenticated = async () => {
  try {
    // Ensure Amplify is configured
    configureAmplify();
    
    const session = await fetchAuthSession();
    return !!session?.tokens?.idToken;
  } catch (error) {
    console.error('Auth check error:', error);
    return false;
  }
}

// Sign out
export const signOut = async () => {
  try {
    // Ensure Amplify is configured
    configureAmplify();
    
    // Sign out from Amplify
    await amplifySignOut({ global: true });
    
    // Clear local storage and cookies
    localStorage.removeItem('isAuthenticated');
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    
    return true;
  } catch (error) {
    console.error('Error signing out:', error);
    
    // Clear local storage and cookies anyway
    localStorage.removeItem('isAuthenticated');
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    
    return true;
  }
}

// JWT payload interface for Cognito tokens
interface CognitoJwtPayload {
  sub: string;
  email: string;
  name?: string;
  exp: number;
  'cognito:groups'?: string[];
  [key: string]: any;
}

// Helper function to parse JWT token (works in both browser and Node.js)
const parseJwtToken = (token: string): CognitoJwtPayload | null => {
  try {
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      return null;
    }

    // Decode the payload part of the JWT
    // Use Buffer for Node.js, atob for browser
    let decodedPayload: string;
    if (typeof window === 'undefined') {
      // Server-side (Node.js)
      decodedPayload = Buffer.from(tokenParts[1], 'base64').toString('utf-8');
    } else {
      // Client-side (browser)
      decodedPayload = atob(tokenParts[1]);
    }

    const payload = JSON.parse(decodedPayload);
    return payload as CognitoJwtPayload;
  } catch (error) {
    console.error('Error parsing JWT token:', error);
    return null;
  }
};

// Server-side function to verify authentication
export const verifyAuth = async (request: Request) => {
  try {
    const authCookie = request.headers.get('Cookie')?.split(';')
      .find(c => c.trim().startsWith('idToken='))
      ?.split('=')[1];

    if (!authCookie) {
      return false;
    }

    // SECURITY ISSUE: Need proper JWT validation with signature verification
    // Current implementation only checks expiration
    const payload = parseJwtToken(authCookie);
    if (!payload || !payload.exp || Date.now() >= payload.exp * 1000) {
      return false;
    }

    // RECOMMENDATION: Add additional checks
    // 1. Verify token signature using Cognito's JWKS
    // 2. Validate issuer (iss) matches your Cognito user pool
    // 3. Validate audience (aud) matches your client ID

    return {
      isAuthenticated: true,
      userId: payload.sub,
      email: payload.email,
      name: payload.name,
      roles: payload['cognito:groups'] || []
    };
  } catch (error) {
    console.error('Auth verification error:', error);
    return false;
  }
}

























