{"name": "saas-application", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "audit": "npm audit --production", "test": "jest"}, "dependencies": {"@aws-sdk/rds-signer": "^3.830.0", "aws-amplify": "^6.0.0", "jose": "^5.1.0", "next": "^15.3.3", "pg": "^8.11.3", "react": "^18.3.1", "react-dom": "^18.2.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.19.0", "@types/pg": "^8.10.9", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-plugin-security": "^1.7.1", "jest": "^29.7.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}}