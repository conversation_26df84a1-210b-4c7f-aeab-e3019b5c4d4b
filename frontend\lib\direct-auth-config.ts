import { Amplify } from 'aws-amplify'

export function configureAmplifyDirectly() {
  // If already configured, don't configure again
  if (Amplify.getConfig().Auth) return;
  
  // Hard-coded values for testing - replace with your actual values
  const config = {
    userPoolId: 'ca-central-1_uwPuGUhLc',
    userPoolClientId: '6fc4ks4poom3mqk5icavr7np1k',
    domain: 'auth.renewtrack.com',
    redirectSignIn: 'http://localhost:3000/callback',
    redirectSignOut: 'http://localhost:3000/signout'
  };
  
  try {
    Amplify.configure({
      Auth: {
        Cognito: {
          userPoolId: config.userPoolId,
          userPoolClientId: config.userPoolClientId,
          loginWith: {
            oauth: {
              domain: config.domain,
              scopes: ["email", "profile", "openid", "aws.cognito.signin.user.admin"],
              redirectSignIn: [config.redirectSignIn],
              redirectSignOut: [config.redirectSignOut],
              responseType: "code"
            }
          }
        }
      }
    }, {
      ssr: true
    });
    console.log('Amplify configured directly with hard-coded values');
  } catch (error) {
    console.error('Error configuring Amplify directly:', error);
  }
}