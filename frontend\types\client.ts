export interface ClientSettings {
  theme?: 'light' | 'dark' | 'system';
  features?: Record<string, boolean>;
  branding?: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
  [key: string]: any;
}

export interface Client {
  id: string;
  name: string;
  domain: string;
  status: 'active' | 'inactive' | 'pending';
  settings?: ClientSettings;
  createdAt?: string;
  updatedAt?: string;
}