#!/bin/bash

# RenewTrack Database Setup Script
# This script helps set up AWS RDS with Parameter Store integration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
AWS_REGION="${AWS_REGION:-ca-central-1}"
DB_INSTANCE_IDENTIFIER="${DB_INSTANCE_IDENTIFIER:-renewtrack-prod-ca-central-1-rds}"
DB_USERNAME="${DB_USERNAME:-renewtrack_admin}"
DB_IAM_USERNAME="${DB_IAM_USERNAME:-renewtrack_iam_user}"

echo -e "${BLUE}RenewTrack Database Setup${NC}"
echo "=========================="
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

print_status "AWS CLI is configured and ready."

# Display current configuration
echo ""
echo "Current Configuration:"
echo "  AWS Region: $AWS_REGION"
echo "  DB Instance: $DB_INSTANCE_IDENTIFIER"
echo "  DB Username: $DB_USERNAME"
echo "  IAM Username: $DB_IAM_USERNAME"
echo ""

# Ask user which authentication method to set up
echo "Which authentication method would you like to set up?"
echo "1) Traditional Username/Password (simpler)"
echo "2) IAM Database Authentication (more secure)"
echo "3) Both (recommended for flexibility)"
echo ""
read -p "Enter your choice (1-3): " auth_choice

case $auth_choice in
    1)
        setup_password_auth=true
        setup_iam_auth=false
        ;;
    2)
        setup_password_auth=false
        setup_iam_auth=true
        ;;
    3)
        setup_password_auth=true
        setup_iam_auth=true
        ;;
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

# Function to setup password authentication
setup_password_authentication() {
    print_status "Setting up traditional username/password authentication..."
    
    # Get database password
    echo ""
    read -s -p "Enter database password for user '$DB_USERNAME': " db_password
    echo ""
    
    if [ -z "$db_password" ]; then
        print_error "Password cannot be empty."
        return 1
    fi
    
    # Store parameters
    print_status "Storing database credentials in Parameter Store..."
    
    aws ssm put-parameter \
        --name "/renewtrack/db/username" \
        --value "$DB_USERNAME" \
        --type "String" \
        --region "$AWS_REGION" \
        --overwrite || print_warning "Failed to store username parameter"
    
    aws ssm put-parameter \
        --name "/renewtrack/db/password" \
        --value "$db_password" \
        --type "SecureString" \
        --region "$AWS_REGION" \
        --overwrite || print_warning "Failed to store password parameter"
    
    print_status "Password authentication setup complete."
}

# Function to setup IAM authentication
setup_iam_authentication() {
    print_status "Setting up IAM database authentication..."
    
    # Enable IAM authentication on RDS instance
    print_status "Enabling IAM database authentication on RDS instance..."
    aws rds modify-db-instance \
        --db-instance-identifier "$DB_INSTANCE_IDENTIFIER" \
        --enable-iam-database-authentication \
        --apply-immediately \
        --region "$AWS_REGION" || print_warning "Failed to enable IAM auth on RDS instance"
    
    # Create IAM policy for RDS connect
    print_status "Creating IAM policy for RDS connect..."
    
    # Get AWS account ID
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    
    # Create policy document
    cat > /tmp/rds-connect-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "rds-db:connect"
            ],
            "Resource": [
                "arn:aws:rds-db:${AWS_REGION}:${ACCOUNT_ID}:dbuser:${DB_INSTANCE_IDENTIFIER}/${DB_IAM_USERNAME}"
            ]
        }
    ]
}
EOF
    
    aws iam create-policy \
        --policy-name RenewTrackRDSConnectPolicy \
        --policy-document file:///tmp/rds-connect-policy.json \
        --region "$AWS_REGION" 2>/dev/null || print_warning "Policy may already exist"
    
    # Clean up temp file
    rm -f /tmp/rds-connect-policy.json
    
    # Store IAM username parameter
    aws ssm put-parameter \
        --name "/renewtrack/db/username" \
        --value "$DB_IAM_USERNAME" \
        --type "String" \
        --region "$AWS_REGION" \
        --overwrite || print_warning "Failed to store IAM username parameter"
    
    print_status "IAM authentication setup complete."
    print_warning "Don't forget to create the IAM database user in PostgreSQL:"
    echo ""
    echo "  CREATE USER $DB_IAM_USERNAME;"
    echo "  GRANT rds_iam TO $DB_IAM_USERNAME;"
    echo "  GRANT CONNECT ON DATABASE your_database_name TO $DB_IAM_USERNAME;"
    echo "  GRANT USAGE ON SCHEMA public TO $DB_IAM_USERNAME;"
    echo "  GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO $DB_IAM_USERNAME;"
    echo "  GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO $DB_IAM_USERNAME;"
    echo ""
}

# Function to setup common parameters
setup_common_parameters() {
    print_status "Setting up common database parameters..."
    
    # Get RDS instance details
    DB_ENDPOINT=$(aws rds describe-db-instances \
        --db-instance-identifier "$DB_INSTANCE_IDENTIFIER" \
        --region "$AWS_REGION" \
        --query 'DBInstances[0].Endpoint.Address' \
        --output text 2>/dev/null || echo "$DB_INSTANCE_IDENTIFIER.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com")
    
    # Store common parameters
    aws ssm put-parameter \
        --name "/renewtrack/db/host" \
        --value "$DB_ENDPOINT" \
        --type "String" \
        --region "$AWS_REGION" \
        --overwrite || print_warning "Failed to store host parameter"
    
    aws ssm put-parameter \
        --name "/renewtrack/db/name" \
        --value "$DB_INSTANCE_IDENTIFIER" \
        --type "String" \
        --region "$AWS_REGION" \
        --overwrite || print_warning "Failed to store database name parameter"
    
    print_status "Common parameters setup complete."
}

# Main execution
echo ""
print_status "Starting database setup..."

# Setup common parameters first
setup_common_parameters

# Setup authentication methods based on user choice
if [ "$setup_password_auth" = true ]; then
    setup_password_authentication
fi

if [ "$setup_iam_auth" = true ]; then
    setup_iam_authentication
fi

# Final instructions
echo ""
print_status "Setup complete!"
echo ""
echo "Next steps:"
echo "1. Deploy the CloudFormation stack:"
echo "   aws cloudformation deploy --template-file cloudformation/parameter-store-role.yaml --stack-name renewtrack-parameter-store-role --capabilities CAPABILITY_NAMED_IAM --region $AWS_REGION"
echo ""
echo "2. Set environment variables in your application:"
if [ "$setup_iam_auth" = true ]; then
    echo "   USE_IAM_DB_AUTH=true"
else
    echo "   USE_IAM_DB_AUTH=false"
fi
echo "   NEXT_PUBLIC_AWS_REGION=$AWS_REGION"
echo "   PARAMETER_STORE_ROLE_ARN=arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/RenewTrackParameterStoreAccess"
echo ""
echo "3. Test the connection using the verification commands in aws-setup-commands.md"
echo ""
print_status "All done! 🎉"
