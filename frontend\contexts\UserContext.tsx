'use client'

import { createContext, useState, useEffect, ReactNode, useContext } from 'react'
import { useAuth } from './AuthContext'
import { useClient } from './ClientContext'

export interface UserPreferences {
  theme?: 'light' | 'dark' | 'system'
  notifications?: {
    email?: boolean
    push?: boolean
    sms?: boolean
  }
  displayDensity?: 'comfortable' | 'compact'
  [key: string]: any
}

export interface User {
  id: string
  email: string
  name?: string
  given_name?: string
  family_name?: string
  roles?: string[]
  clientId?: string
  preferences?: UserPreferences
  lastLogin?: Date
}

interface UserContextType {
  user: User | null
  isLoading: boolean
  updateUserProfile: (data: Partial<User>) => Promise<boolean>
  updateUserPreferences: (preferences: Partial<UserPreferences>) => Promise<boolean>
  getDisplayName: () => string
  hasRole: (role: string) => boolean
}

// Create context with default values
const UserContext = createContext<UserContextType>({
  user: null,
  isLoading: false,
  updateUserProfile: async () => false,
  updateUserPreferences: async () => false,
  getDisplayName: () => 'User',
  hasRole: () => false
})

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { isAuthenticated, authUser } = useAuth()
  const { client } = useClient()

  // Update user when auth state changes
  useEffect(() => {
    if (isAuthenticated && authUser) {
      // Initialize user from auth data
      setUser({
        ...authUser,
        clientId: authUser.clientId || client?.id,
        preferences: user?.preferences || {
          theme: 'system',
          notifications: {
            email: true,
            push: true,
            sms: false
          },
          displayDensity: 'comfortable'
        }
      })
    } else {
      setUser(null)
    }
  }, [isAuthenticated, authUser, client])

  // Load user preferences from API or localStorage
  useEffect(() => {
    if (!user?.id) return

    const loadUserPreferences = async () => {
      try {
        setIsLoading(true)
        
        // Try to load from localStorage first (for demo/development)
        const storedPrefs = localStorage.getItem(`user_prefs_${user.id}`)
        if (storedPrefs) {
          const parsedPrefs = JSON.parse(storedPrefs)
          setUser(prev => prev ? { ...prev, preferences: parsedPrefs } : null)
          return
        }
        
        // If not in localStorage, fetch from API
        const response = await fetch(`/api/users/${user.id}/preferences`)
        if (response.ok) {
          const preferences = await response.json()
          setUser(prev => prev ? { ...prev, preferences } : null)
          
          // Cache in localStorage
          localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(preferences))
        }
      } catch (error) {
        console.error('Error loading user preferences:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadUserPreferences()
  }, [user?.id])

  // Update user profile
  const updateUserProfile = async (data: Partial<User>): Promise<boolean> => {
    if (!user?.id) return false
    
    try {
      setIsLoading(true)
      const response = await fetch(`/api/users/${user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        throw new Error('Failed to update profile')
      }
      
      const updatedUser = await response.json()
      setUser(prev => prev ? { ...prev, ...updatedUser } : null)
      return true
    } catch (error) {
      console.error('Error updating user profile:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Update user preferences
  const updateUserPreferences = async (preferences: Partial<UserPreferences>): Promise<boolean> => {
    if (!user?.id) return false
    
    try {
      setIsLoading(true)
      
      // Update in state first for immediate feedback
      const updatedPreferences = { ...user.preferences, ...preferences }
      setUser(prev => prev ? { ...prev, preferences: updatedPreferences } : null)
      
      // Cache in localStorage
      localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(updatedPreferences))
      
      // Then update on server
      const response = await fetch(`/api/users/${user.id}/preferences`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(preferences)
      })
      
      if (!response.ok) {
        throw new Error('Failed to update preferences')
      }
      
      return true
    } catch (error) {
      console.error('Error updating user preferences:', error)
      // Revert to previous state on error
      const storedPrefs = localStorage.getItem(`user_prefs_${user.id}`)
      if (storedPrefs) {
        const parsedPrefs = JSON.parse(storedPrefs)
        setUser(prev => prev ? { ...prev, preferences: parsedPrefs } : null)
      }
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Get formatted display name
  const getDisplayName = (): string => {
    if (!user) return 'User'
    
    if (user.given_name && user.family_name) {
      return `${user.given_name} ${user.family_name}`
    }
    
    if (user.name) {
      return user.name
    }
    
    // Check if email looks like a UUID or system-generated value
    const isUUID = (str: string) => {
      return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(str)
    }
    
    if (user.email && !isUUID(user.email.split('@')[0])) {
      return user.email.split('@')[0]
    }
    
    return `User ${user.id.substring(0, 8)}`
  }

  // Role checking utility
  const hasRole = (role: string): boolean => {
    return user?.roles?.includes(role) || false
  }

  return (
    <UserContext.Provider
      value={{
        user,
        isLoading,
        updateUserProfile,
        updateUserPreferences,
        getDisplayName,
        hasRole
      }}
    >
      {children}
    </UserContext.Provider>
  )
}

// Custom hook for using user context
export const useUser = () => useContext(UserContext)







