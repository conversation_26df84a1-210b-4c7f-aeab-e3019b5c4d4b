import { NextRequest, NextResponse } from 'next/server'
import { getLoginUrl } from './lib/auth'

// Define which paths require authentication
const protectedPaths = [
  '/dashboard',
  '/profile',
  '/settings',
  '/clients',
  '/renewals'
]

// Define which paths should redirect if already authenticated
const authPaths = [
  '/login',
  '/signup'
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Check if the path is protected
  const isProtectedPath = protectedPaths.some(path => 
    pathname === path || pathname.startsWith(`${path}/`)
  )
  
  // Check if the path is an auth path
  const isAuthPath = authPaths.some(path => 
    pathname === path || pathname.startsWith(`${path}/`)
  )
  
  // Get auth cookie
  const authCookie = request.cookies.get('idToken')
  const isAuthenticated = !!authCookie?.value
  
  // Create the response
  const response = NextResponse.next()
  
  // Add security headers
  const securityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  }
  
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  // Handle protected routes - redirect to login if not authenticated
  if (isProtectedPath && !isAuthenticated) {
    const loginUrl = getLoginUrl()
    return NextResponse.redirect(loginUrl)
  }
  
  // Handle auth routes - redirect to dashboard if already authenticated
  if (isAuthPath && isAuthenticated) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }
  
  return response
}

// Configure middleware to run only on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes (except those that need auth checks)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api/public).*)',
  ],
}


