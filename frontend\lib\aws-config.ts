import { STSClient, AssumeRoleCommand } from "@aws-sdk/client-sts";
import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";

// Function to get temporary credentials by assuming a role
async function getCredentials() {
  if (process.env.NODE_ENV !== 'production') {
    return {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.NEXT_PUBLIC_AWS_REGION
    };
  }

  const stsClient = new STSClient({ region: process.env.NEXT_PUBLIC_AWS_REGION });
  
  const command = new AssumeRoleCommand({
    RoleArn: process.env.PARAMETER_STORE_ROLE_ARN,
    RoleSessionName: 'RenewTrackParameterStoreAccess',
    DurationSeconds: 900 // 15 minutes
  });
  
  const response = await stsClient.send(command);
  
  return {
    accessKeyId: response.Credentials?.AccessKeyId,
    secretAccessKey: response.Credentials?.SecretAccessKey,
    sessionToken: response.Credentials?.SessionToken,
    region: process.env.NEXT_PUBLIC_AWS_REGION
  };
}

// Get parameter from SSM Parameter Store with assumed role
export async function getParameter(paramName: string) {
  const credentials = await getCredentials();
  
  const ssmClient = new SSMClient({
    region: process.env.NEXT_PUBLIC_AWS_REGION || 'ca-central-1',
    credentials: {
      accessKeyId: credentials.accessKeyId || '',
      secretAccessKey: credentials.secretAccessKey || '',
      sessionToken: credentials.sessionToken
    }
  });
  
  const command = new GetParameterCommand({
    Name: paramName,
    WithDecryption: true
  });
  
  const response = await ssmClient.send(command);
  return response.Parameter?.Value;
}


