import { NextRequest, NextResponse } from 'next/server';
import { createClient, updateClient } from '../../../lib/clients';
import { verifySession } from '../../../lib/dal';
import { z } from 'zod';

// Define schema for client creation
const createClientSchema = z.object({
  name: z.string().min(2).max(255),
  domain: z.string().min(3).max(255).regex(
    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/,
    'Invalid domain format'
  ),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
});

// Define schema for client updates
const updateClientSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  domain: z.string().min(3).max(255).regex(
    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/,
    'Invalid domain format'
  ).optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
});

export async function POST(request: NextRequest) {
  // Verify authentication and authorization
  const session = await verifySession();
  if (!session || !session.roles.includes('admin')) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    
    // Validate input
    try {
      createClientSchema.parse(body);
    } catch (error) {
      return NextResponse.json({ 
        error: 'Invalid client data',
        details: error instanceof Error ? error.message : 'Validation error'
      }, { status: 400 });
    }
    
    const client = await createClient({
      name: body.name,
      domain: body.domain,
      status: body.status,
      settings: body.settings
    });
    
    return NextResponse.json(client, { status: 201 });
  } catch (error) {
    console.error('Error creating client:', error);
    return NextResponse.json({ error: 'Failed to create client' }, { status: 500 });
  }
}