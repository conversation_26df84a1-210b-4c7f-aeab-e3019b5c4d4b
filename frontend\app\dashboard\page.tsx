
'use client'

import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function DashboardPage() {
  const router = useRouter()
  const { user } = useAuth()

  // Get display name from user data
  const getDisplayName = () => {
    if (!user) return 'User'
    
    if (user.given_name && user.family_name) {
      return `${user.given_name} ${user.family_name}`
    }
    
    if (user.name) {
      return user.name
    }
    
    if (user.email) {
      return user.email.split('@')[0]
    }
    
    return 'User'
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Welcome back, {getDisplayName()}!</h2>
        <div className="mb-4">
          <p className="text-gray-600 mb-2">You are now signed in and can access all features.</p>
          {user?.email && (
            <p className="text-sm text-gray-500">Signed in as: {user.email}</p>
          )}
          {user?.roles && user.roles.length > 0 && (
            <p className="text-sm text-gray-500">Roles: {user.roles.join(', ')}</p>
          )}
        </div>

        <button
          onClick={() => router.push('/signout')}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Sign Out
        </button>
      </div>
    </div>
  )
}

