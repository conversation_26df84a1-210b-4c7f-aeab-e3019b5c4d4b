import { createContext, useState, useContext, ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Client, ClientSettings } from '@/types/client';

interface ClientContextType {
  client: Client | null;
  isLoading: boolean;
  getClientByDomain: (domain: string) => Promise<Client | null>;
  getClientById: (id: string) => Promise<Client | null>;
  getClientByEmail: (email: string) => Promise<Client | null>;
  updateClientSettings: (settings: Partial<ClientSettings>) => Promise<boolean>;
}

// Create context with default values
const ClientContext = createContext<ClientContextType>({
  client: null,
  isLoading: false,
  getClientByDomain: async () => null,
  getClientById: async () => null,
  getClientByEmail: async () => null,
  updateClientSettings: async () => false
});

export function ClientProvider({ children }: { children: ReactNode }) {
  const [client, setClient] = useState<Client | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  // Function to get client info from email domain
  const getClientByDomain = async (domain: string): Promise<Client | null> => {
    if (!domain) return null;
    
    try {
      setIsLoading(true);
      const response = await fetch(`/api/clients/domain?domain=${encodeURIComponent(domain)}`);
      
      if (!response.ok) {
        if (response.status !== 404) {
          console.error('Error fetching client:', await response.text());
        }
        return null;
      }
      
      const clientData = await response.json();
      setClient(clientData);
      return clientData;
    } catch (error) {
      console.error('Error getting client info:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to get client by ID
  const getClientById = async (id: string): Promise<Client | null> => {
    if (!id) return null;
    
    try {
      setIsLoading(true);
      const response = await fetch(`/api/clients/${encodeURIComponent(id)}`);
      
      if (!response.ok) {
        if (response.status !== 404) {
          console.error('Error fetching client:', await response.text());
        }
        return null;
      }
      
      const clientData = await response.json();
      setClient(clientData);
      return clientData;
    } catch (error) {
      console.error('Error getting client info:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to update client settings
  const updateClientSettings = async (settings: Partial<ClientSettings>): Promise<boolean> => {
    if (!client?.id) return false;
    
    try {
      setIsLoading(true);
      const response = await fetch(`/api/clients/${encodeURIComponent(client.id)}/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });
      
      if (!response.ok) {
        console.error('Error updating client settings:', await response.text());
        return false;
      }
      
      const updatedClient = await response.json();
      setClient(updatedClient);
      return true;
    } catch (error) {
      console.error('Error updating client settings:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to get client by email
  const getClientByEmail = async (email: string): Promise<Client | null> => {
    if (!email) return null;
    
    try {
      setIsLoading(true);
      const response = await fetch(`/api/clients/domain?email=${encodeURIComponent(email)}`);
      
      if (!response.ok) {
        if (response.status !== 404) {
          console.error('Error fetching client:', await response.text());
        }
        return null;
      }
      
      const clientData = await response.json();
      setClient(clientData);
      return clientData;
    } catch (error) {
      console.error('Error getting client info by email:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ClientContext.Provider
      value={{
        client,
        isLoading,
        getClientByDomain,
        getClientById,
        getClientByEmail,
        updateClientSettings
      }}
    >
      {children}
    </ClientContext.Provider>
  );
}

// Custom hook for using client context
export const useClient = () => useContext(ClientContext);

