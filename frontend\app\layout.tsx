
import { Inter } from 'next/font/google'
import '../styles/globals.css'
import { configureAmplify } from '../lib/amplify-config'
import { Providers } from '@/components/providers'

// Configure Amplify at the application root
configureAmplify()

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'RenewTrack',
  description: 'Track and manage your renewals',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}

