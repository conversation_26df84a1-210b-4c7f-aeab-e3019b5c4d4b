-- Create clients table in tenant_management schema
CREATE TABLE IF NOT EXISTS tenant_management.clients (
    client_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NOT NULL UNIQUE,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on domain for faster lookups
CREATE INDEX IF NOT EXISTS idx_clients_domain ON tenant_management.clients(domain);

-- Add trigger to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION tenant_management.update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_clients_timestamp
BEFORE UPDATE ON tenant_management.clients
FOR EACH ROW
EXECUTE FUNCTION tenant_management.update_timestamp();

-- Sample data (remove in production)
INSERT INTO tenant_management.clients (name, domain, status, settings)
VALUES 
    ('Acme Corporation', 'acme.com', 'active', '{"theme": "light", "features": {"analytics": true}}'),
    ('Globex Industries', 'globex.com', 'active', '{"theme": "dark", "features": {"analytics": true}}')
ON CONFLICT (domain) DO NOTHING;