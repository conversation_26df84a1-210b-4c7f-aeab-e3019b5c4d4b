-- Create tenant management schema
CREATE SCHEMA IF NOT EXISTS tenant_management;

-- Tenants table
CREATE TABLE IF NOT EXISTS tenant_management.tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_name VARCHAR(255) NOT NULL,
    schema_name VARCHAR(63) NOT NULL UNIQUE,
    subdomain VARCHAR(100) UNIQUE,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_tenant_subdomain ON tenant_management.tenants(subdomain);
CREATE INDEX IF NOT EXISTS idx_tenant_schema ON tenant_management.tenants(schema_name);

-- Domains table for email domain mapping
CREATE TABLE IF NOT EXISTS tenant_management.domains (
    domain_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenant_management.tenants(tenant_id) ON DELETE CASCADE,
    domain_name VARCHAR(255) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_domain UNIQUE (domain_name)
);

-- Create index for faster domain lookups
CREATE INDEX IF NOT EXISTS idx_domains_domain_name ON tenant_management.domains(domain_name);
