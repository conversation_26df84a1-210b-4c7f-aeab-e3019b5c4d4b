import { Amplify } from 'aws-amplify'

// Amplify configuration using environment variables with fallbacks
export function configureAmplify() {
  // Only configure once
  if (Amplify.getConfig().Auth) {
    console.log('Amplify already configured, skipping...');
    return;
  }

  // Get configuration from environment variables with fallbacks
  const config = {
    region: process.env.NEXT_PUBLIC_AWS_REGION || 'ca-central-1',
    userPoolId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID || 'ca-central-1_uwPuGUhLc',
    userPoolClientId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID || '6fc4ks4poom3mqk5icavr7np1k',
    cognitoDomain: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN || 'renewtrack.auth.ca-central-1.amazoncognito.com',
    redirectSignIn: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN || 'http://localhost:3000/callback',
    redirectSignOut: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT || 'http://localhost:3000/login'
  };

  console.log('Configuring Amplify with:', {
    region: config.region,
    userPoolId: config.userPoolId,
    userPoolClientId: config.userPoolClientId,
    cognitoDomain: config.cognitoDomain,
    redirectSignIn: config.redirectSignIn,
    redirectSignOut: config.redirectSignOut
  });

  try {
    Amplify.configure({
      Auth: {
        Cognito: {
          userPoolId: config.userPoolId,
          userPoolClientId: config.userPoolClientId,
          loginWith: {
            oauth: {
              domain: config.cognitoDomain,
              scopes: ["email", "profile", "openid", "aws.cognito.signin.user.admin"],
              redirectSignIn: [config.redirectSignIn],
              redirectSignOut: [config.redirectSignOut],
              responseType: "code"
            }
          }
        }
      }
    }, {
      ssr: true
    });
    console.log('Amplify configured successfully with OAuth domain:', config.cognitoDomain);
  } catch (error) {
    console.error('Error configuring Amplify:', error);
    throw error;
  }
}

// Export configured instance for server-side operations
export function getAmplifySSR(_request: Request) {
  // For Amplify v6, we need to create a new instance with SSR context
  return {
    Auth: Amplify.getConfig().Auth
  }
}















